"use client";

import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useDanceClasses } from "@/lib/queries";

const days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];

export function ClassSchedule() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const { data: classes, isLoading } = useDanceClasses();

  // Create schedule data organized by day
  const scheduleByDay = days.reduce((acc, day) => {
    acc[day] = [];
    classes?.forEach((danceClass) => {
      danceClass.schedule.forEach((schedule) => {
        if (schedule.day === day) {
          acc[day].push({
            ...danceClass,
            time: schedule.time,
          });
        }
      });
    });
    // Sort by time
    acc[day].sort((a, b) => {
      const timeA = a.time.split(" - ")[0];
      const timeB = b.time.split(" - ")[0];
      return timeA.localeCompare(timeB);
    });
    return acc;
  }, {} as Record<string, any[]>);

  if (isLoading) {
    return (
      <section className="section-padding bg-muted/50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
              Weekly <span className="gradient-text">Schedule</span>
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {days.slice(0, 4).map((day) => (
              <div key={day} className="dance-card animate-pulse">
                <div className="h-6 bg-muted rounded mb-4" />
                <div className="space-y-2">
                  <div className="h-4 bg-muted rounded" />
                  <div className="h-4 bg-muted rounded" />
                  <div className="h-4 bg-muted rounded" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="section-padding bg-muted/50" ref={ref}>
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
            Weekly <span className="gradient-text">Schedule</span>
          </h2>
          <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto">
            Plan your week with our comprehensive class schedule. Find the perfect 
            time slots that fit your lifestyle.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {days.map((day, dayIndex) => (
            <motion.div
              key={day}
              initial={{ opacity: 0, y: 50 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: dayIndex * 0.1 }}
            >
              <Card className="dance-card h-full">
                <CardHeader className="pb-4">
                  <CardTitle className="text-lg text-center">{day}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {scheduleByDay[day].length > 0 ? (
                    scheduleByDay[day].map((classItem, index) => (
                      <div
                        key={`${classItem.id}-${index}`}
                        className="p-3 bg-background rounded-lg border border-border hover:shadow-md transition-shadow"
                      >
                        <div className="flex items-center justify-between mb-2">
                          <Badge variant="secondary" className="text-xs">
                            {classItem.category}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {classItem.duration}
                          </span>
                        </div>
                        <h4 className="font-semibold text-sm mb-1">
                          {classItem.name}
                        </h4>
                        <p className="text-xs text-muted-foreground mb-2">
                          {classItem.time}
                        </p>
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-muted-foreground">
                            {classItem.ageGroup}
                          </span>
                          <Badge variant="outline" className="text-xs">
                            {classItem.level}
                          </Badge>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <p className="text-sm">No classes scheduled</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-16 text-center"
        >
          <Card className="bg-primary/5 border-primary/20">
            <CardContent className="p-8">
              <h3 className="text-2xl font-bold mb-4">Can't Find a Suitable Time?</h3>
              <p className="text-lg text-muted-foreground mb-6 max-w-2xl mx-auto">
                We're always looking to accommodate our students' schedules. Contact us 
                to discuss additional time slots or private lessons.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 rounded-lg font-semibold transition-colors">
                  Request Custom Schedule
                </button>
                <button className="border border-primary text-primary hover:bg-primary hover:text-primary-foreground px-8 py-3 rounded-lg font-semibold transition-colors">
                  Private Lessons
                </button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
}
