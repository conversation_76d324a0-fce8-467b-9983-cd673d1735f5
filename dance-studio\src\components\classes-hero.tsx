"use client";

import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";

export function ClassesHero() {
  return (
    <section className="relative min-h-[60vh] flex items-center justify-center overflow-hidden bg-gradient-to-br from-primary/10 to-purple-500/10">
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6">
            Our <span className="gradient-text">Dance Classes</span>
          </h1>
          <p className="text-xl sm:text-2xl text-muted-foreground max-w-3xl mx-auto mb-8">
            Discover your passion through our diverse range of dance styles, 
            designed for all ages and skill levels.
          </p>
          <Button size="lg" className="bg-primary hover:bg-primary/90 px-8 py-4">
            Book Your Trial Class
          </Button>
        </motion.div>
      </div>
    </section>
  );
}
