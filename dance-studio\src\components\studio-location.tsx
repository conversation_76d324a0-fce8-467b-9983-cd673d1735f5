"use client";

import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MapPin, Car, Bus, Clock, CreditCard, RefreshCw, Gift } from "lucide-react";

export function StudioLocation() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <section className="section-padding bg-background" ref={ref}>
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
            Find Our <span className="gradient-text">Studio</span>
          </h2>
          <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto">
            Located in the heart of Biratnagar, our studio is easily accessible 
            and surrounded by convenient amenities.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Map Placeholder */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8 }}
          >
            <Card className="dance-card">
              <CardContent className="p-0">
                <div className="h-96 bg-gradient-to-br from-primary/20 to-purple-500/20 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <MapPin className="w-16 h-16 text-primary mx-auto mb-4" />
                    <h3 className="text-xl font-semibold mb-2">Interactive Map</h3>
                    <p className="text-muted-foreground mb-4">
                      DanceStudio Biratnagar<br />
                      Main Road, Biratnagar, Nepal
                    </p>
                    <Button className="bg-primary hover:bg-primary/90">
                      Open in Google Maps
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Location Details */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="space-y-6"
          >
            <div>
              <h3 className="text-2xl font-bold mb-4">Getting Here</h3>
              <p className="text-muted-foreground mb-6">
                Our studio is strategically located for easy access from all parts of Biratnagar. 
                Here's how you can reach us:
              </p>
            </div>

            <div className="space-y-4">
              <Card className="dance-card">
                <CardContent className="p-4">
                  <div className="flex items-start space-x-4">
                    <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                      <Car className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold mb-1">By Car/Motorcycle</h4>
                      <p className="text-sm text-muted-foreground">
                        Free parking available. Located on Main Road, easily accessible from 
                        all major routes. GPS coordinates: 26.4525° N, 87.2718° E
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="dance-card">
                <CardContent className="p-4">
                  <div className="flex items-start space-x-4">
                    <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                      <Bus className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold mb-1">Public Transport</h4>
                      <p className="text-sm text-muted-foreground">
                        Multiple bus routes stop nearby. Closest stop: City Center Mall 
                        (2-minute walk). Auto-rickshaws and taxis readily available.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="dance-card">
                <CardContent className="p-4">
                  <div className="flex items-start space-x-4">
                    <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                      <Clock className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold mb-1">Travel Time</h4>
                      <p className="text-sm text-muted-foreground">
                        From Airport: 15 minutes • From Bus Park: 10 minutes • 
                        From Railway Station: 12 minutes
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card className="bg-primary/5 border-primary/20">
              <CardContent className="p-6">
                <h4 className="font-semibold mb-3">Nearby Landmarks</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• City Center Mall (2 min walk)</li>
                  <li>• Biratnagar Hospital (5 min walk)</li>
                  <li>• Mahendra Park (3 min walk)</li>
                  <li>• Main Post Office (4 min walk)</li>
                  <li>• Several restaurants and cafes nearby</li>
                </ul>
              </CardContent>
            </Card>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button className="flex-1 bg-primary hover:bg-primary/90">
                Get Directions
              </Button>
              <Button variant="outline" className="flex-1">
                Call for Directions
              </Button>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
