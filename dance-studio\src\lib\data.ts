// Mock data for the dance studio

export interface DanceClass {
  id: string;
  name: string;
  description: string;
  category: string;
  ageGroup: string;
  duration: string;
  level: string;
  image: string;
  schedule: {
    day: string;
    time: string;
  }[];
}

export interface Instructor {
  id: string;
  name: string;
  bio: string;
  specialties: string[];
  experience: string;
  image: string;
}

export interface Testimonial {
  id: string;
  name: string;
  role: string;
  content: string;
  rating: number;
  image: string;
}

export interface Package {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: string;
  features: string[];
  popular: boolean;
}

export const danceClasses: DanceClass[] = [
  {
    id: "1",
    name: "Hip-Hop Fundamentals",
    description: "Learn the basics of hip-hop dance with energetic moves and urban style.",
    category: "Hip-Hop",
    ageGroup: "13+",
    duration: "60 minutes",
    level: "Beginner",
    image: "/images/hip-hop.jpg",
    schedule: [
      { day: "Monday", time: "6:00 PM - 7:00 PM" },
      { day: "Wednesday", time: "6:00 PM - 7:00 PM" },
      { day: "Friday", time: "6:00 PM - 7:00 PM" }
    ]
  },
  {
    id: "2",
    name: "Contemporary Flow",
    description: "Express yourself through fluid movements and emotional storytelling.",
    category: "Contemporary",
    ageGroup: "16+",
    duration: "75 minutes",
    level: "Intermediate",
    image: "/images/contemporary.jpg",
    schedule: [
      { day: "Tuesday", time: "7:00 PM - 8:15 PM" },
      { day: "Thursday", time: "7:00 PM - 8:15 PM" }
    ]
  },
  {
    id: "3",
    name: "Classical Dance",
    description: "Traditional Nepali and Indian classical dance forms with grace and precision.",
    category: "Classical",
    ageGroup: "8+",
    duration: "90 minutes",
    level: "All Levels",
    image: "/images/classical.jpg",
    schedule: [
      { day: "Saturday", time: "10:00 AM - 11:30 AM" },
      { day: "Sunday", time: "10:00 AM - 11:30 AM" }
    ]
  },
  {
    id: "4",
    name: "Zumba Fitness",
    description: "High-energy dance fitness combining Latin rhythms with easy-to-follow moves.",
    category: "Zumba",
    ageGroup: "18+",
    duration: "45 minutes",
    level: "All Levels",
    image: "/images/zumba.jpg",
    schedule: [
      { day: "Monday", time: "7:00 AM - 7:45 AM" },
      { day: "Wednesday", time: "7:00 AM - 7:45 AM" },
      { day: "Friday", time: "7:00 AM - 7:45 AM" }
    ]
  },
  {
    id: "5",
    name: "Kids Dance Party",
    description: "Fun and creative dance classes designed specifically for children.",
    category: "Kids",
    ageGroup: "5-12",
    duration: "45 minutes",
    level: "Beginner",
    image: "/images/kids-dance.jpg",
    schedule: [
      { day: "Saturday", time: "2:00 PM - 2:45 PM" },
      { day: "Sunday", time: "2:00 PM - 2:45 PM" }
    ]
  }
];

export const instructors: Instructor[] = [
  {
    id: "1",
    name: "Priya Sharma",
    bio: "Lead instructor with over 8 years of experience in contemporary and classical dance.",
    specialties: ["Contemporary", "Classical", "Choreography"],
    experience: "8+ years",
    image: "/images/instructor-1.jpg"
  },
  {
    id: "2",
    name: "Raj Thapa",
    bio: "Hip-hop specialist who brings street dance culture to Biratnagar with authentic moves.",
    specialties: ["Hip-Hop", "Breaking", "Urban Dance"],
    experience: "6+ years",
    image: "/images/instructor-2.jpg"
  },
  {
    id: "3",
    name: "Maya Gurung",
    bio: "Certified Zumba instructor passionate about fitness and dance fusion.",
    specialties: ["Zumba", "Dance Fitness", "Latin Dance"],
    experience: "5+ years",
    image: "/images/instructor-3.jpg"
  },
  {
    id: "4",
    name: "Arjun Rai",
    bio: "Kids dance specialist who makes learning fun and engaging for young dancers.",
    specialties: ["Kids Dance", "Creative Movement", "Musical Theatre"],
    experience: "4+ years",
    image: "/images/instructor-4.jpg"
  }
];

export const testimonials: Testimonial[] = [
  {
    id: "1",
    name: "Sita Poudel",
    role: "Student",
    content: "DanceStudio has transformed my confidence and fitness. The instructors are amazing and the community is so welcoming!",
    rating: 5,
    image: "/images/testimonial-1.jpg"
  },
  {
    id: "2",
    name: "Ramesh Shrestha",
    role: "Parent",
    content: "My daughter loves her kids dance classes here. She's learned so much and made great friends. Highly recommend!",
    rating: 5,
    image: "/images/testimonial-2.jpg"
  },
  {
    id: "3",
    name: "Anita Karki",
    role: "Student",
    content: "The contemporary classes are incredible. I've grown so much as a dancer and performer. Thank you DanceStudio!",
    rating: 5,
    image: "/images/testimonial-3.jpg"
  }
];

export const packages: Package[] = [
  {
    id: "1",
    name: "Drop-In Class",
    description: "Perfect for trying out classes or occasional visits",
    price: 500,
    duration: "Single Class",
    features: [
      "Access to any single class",
      "No commitment required",
      "Great for beginners"
    ],
    popular: false
  },
  {
    id: "2",
    name: "Weekly Package",
    description: "Ideal for regular practice and skill development",
    price: 1800,
    duration: "1 Week",
    features: [
      "Unlimited classes for 1 week",
      "Access to all dance styles",
      "Priority booking",
      "Free water bottle"
    ],
    popular: true
  },
  {
    id: "3",
    name: "Monthly Unlimited",
    description: "Best value for dedicated dancers",
    price: 6000,
    duration: "1 Month",
    features: [
      "Unlimited classes for 1 month",
      "Access to all dance styles",
      "Priority booking",
      "Free merchandise",
      "Guest pass (1 per month)"
    ],
    popular: false
  }
];
