import { useQuery } from '@tanstack/react-query';
import { danceClasses, instructors, testimonials, packages } from './data';

// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Mock API functions
export const fetchDanceClasses = async () => {
  await delay(500);
  return danceClasses;
};

export const fetchInstructors = async () => {
  await delay(500);
  return instructors;
};

export const fetchTestimonials = async () => {
  await delay(500);
  return testimonials;
};

export const fetchPackages = async () => {
  await delay(500);
  return packages;
};

// React Query hooks
export const useDanceClasses = () => {
  return useQuery({
    queryKey: ['danceClasses'],
    queryFn: fetchDanceClasses,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useInstructors = () => {
  return useQuery({
    queryKey: ['instructors'],
    queryFn: fetchInstructors,
    staleTime: 5 * 60 * 1000,
  });
};

export const useTestimonials = () => {
  return useQuery({
    queryKey: ['testimonials'],
    queryFn: fetchTestimonials,
    staleTime: 5 * 60 * 1000,
  });
};

export const usePackages = () => {
  return useQuery({
    queryKey: ['packages'],
    queryFn: fetchPackages,
    staleTime: 5 * 60 * 1000,
  });
};
