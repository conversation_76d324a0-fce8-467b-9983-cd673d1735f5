"use client";

import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { X, ChevronLeft, ChevronRight, Camera } from "lucide-react";

// Mock gallery data
const galleryImages = [
  {
    id: 1,
    src: "/images/gallery/performance-1.jpg",
    alt: "Hip-Hop Performance",
    category: "Performances",
    title: "Annual Dance Competition 2024",
    description: "Our students showcasing their hip-hop skills at the regional competition."
  },
  {
    id: 2,
    src: "/images/gallery/class-1.jpg",
    alt: "Contemporary Class",
    category: "Classes",
    title: "Contemporary Dance Session",
    description: "Students practicing fluid movements in our contemporary class."
  },
  {
    id: 3,
    src: "/images/gallery/event-1.jpg",
    alt: "Studio Opening",
    category: "Events",
    title: "Grand Opening Celebration",
    description: "The memorable day when DanceStudio opened its doors to the community."
  },
  {
    id: 4,
    src: "/images/gallery/performance-2.jpg",
    alt: "Classical Dance",
    category: "Performances",
    title: "Cultural Festival Performance",
    description: "Beautiful classical dance performance at the local cultural festival."
  },
  {
    id: 5,
    src: "/images/gallery/class-2.jpg",
    alt: "Kids Class",
    category: "Classes",
    title: "Kids Dance Workshop",
    description: "Young dancers learning and having fun in our kids program."
  },
  {
    id: 6,
    src: "/images/gallery/event-2.jpg",
    alt: "Workshop",
    category: "Events",
    title: "Master Class Workshop",
    description: "Special workshop with guest choreographer from Kathmandu."
  },
  {
    id: 7,
    src: "/images/gallery/performance-3.jpg",
    alt: "Zumba Session",
    category: "Classes",
    title: "High-Energy Zumba Class",
    description: "Fitness meets fun in our popular Zumba sessions."
  },
  {
    id: 8,
    src: "/images/gallery/event-3.jpg",
    alt: "Awards Ceremony",
    category: "Events",
    title: "Student Achievement Awards",
    description: "Celebrating our students' achievements and milestones."
  },
  {
    id: 9,
    src: "/images/gallery/performance-4.jpg",
    alt: "Group Performance",
    category: "Performances",
    title: "Community Showcase",
    description: "All dance styles coming together for a spectacular show."
  }
];

const categories = ["All", "Performances", "Classes", "Events"];

export function GalleryGrid() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [selectedCategory, setSelectedCategory] = useState("All");
  const [selectedImage, setSelectedImage] = useState<number | null>(null);

  const filteredImages = galleryImages.filter(
    (image) => selectedCategory === "All" || image.category === selectedCategory
  );

  const openLightbox = (imageId: number) => {
    setSelectedImage(imageId);
  };

  const closeLightbox = () => {
    setSelectedImage(null);
  };

  const navigateImage = (direction: "prev" | "next") => {
    if (selectedImage === null) return;
    
    const currentIndex = filteredImages.findIndex(img => img.id === selectedImage);
    let newIndex;
    
    if (direction === "prev") {
      newIndex = currentIndex > 0 ? currentIndex - 1 : filteredImages.length - 1;
    } else {
      newIndex = currentIndex < filteredImages.length - 1 ? currentIndex + 1 : 0;
    }
    
    setSelectedImage(filteredImages[newIndex].id);
  };

  const selectedImageData = filteredImages.find(img => img.id === selectedImage);

  return (
    <section className="section-padding bg-background" ref={ref}>
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
            Moments That <span className="gradient-text">Inspire</span>
          </h2>
          <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            Every picture tells a story of passion, dedication, and the joy of dance. 
            Browse through our collection of memorable moments.
          </p>

          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-2 mb-12">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                onClick={() => setSelectedCategory(category)}
                className="mb-2"
              >
                {category}
              </Button>
            ))}
          </div>
        </motion.div>

        {/* Gallery Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredImages.map((image, index) => (
            <motion.div
              key={image.id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={inView ? { opacity: 1, scale: 1 } : {}}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="group cursor-pointer"
              onClick={() => openLightbox(image.id)}
            >
              <div className="relative overflow-hidden rounded-lg bg-gradient-to-br from-primary/20 to-purple-500/20 aspect-square">
                {/* Placeholder for image */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <Camera className="w-16 h-16 text-primary opacity-30" />
                </div>
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/50 transition-all duration-300 flex items-end">
                  <div className="p-4 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                    <Badge className="mb-2 bg-primary">
                      {image.category}
                    </Badge>
                    <h3 className="font-semibold text-lg mb-1">{image.title}</h3>
                    <p className="text-sm opacity-90">{image.description}</p>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Lightbox */}
        {selectedImage && selectedImageData && (
          <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4">
            <div className="relative max-w-4xl max-h-full">
              {/* Close button */}
              <button
                onClick={closeLightbox}
                className="absolute top-4 right-4 z-10 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>

              {/* Navigation buttons */}
              <button
                onClick={() => navigateImage("prev")}
                className="absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
              >
                <ChevronLeft className="w-6 h-6" />
              </button>
              <button
                onClick={() => navigateImage("next")}
                className="absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
              >
                <ChevronRight className="w-6 h-6" />
              </button>

              {/* Image */}
              <div className="bg-gradient-to-br from-primary/20 to-purple-500/20 rounded-lg aspect-video flex items-center justify-center">
                <Camera className="w-20 h-20 text-primary opacity-30" />
              </div>

              {/* Image info */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6 text-white">
                <Badge className="mb-2 bg-primary">
                  {selectedImageData.category}
                </Badge>
                <h3 className="text-2xl font-bold mb-2">{selectedImageData.title}</h3>
                <p className="text-lg opacity-90">{selectedImageData.description}</p>
              </div>
            </div>
          </div>
        )}

        {filteredImages.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <p className="text-lg text-muted-foreground">
              No images found for the selected category.
            </p>
          </motion.div>
        )}
      </div>
    </section>
  );
}
