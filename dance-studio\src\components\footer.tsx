import Link from "next/link";
import { Facebook, Instagram, Youtube, MapPin, Phone, Mail } from "lucide-react";

export function Footer() {
  return (
    <footer className="bg-black text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Studio Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-lg">D</span>
              </div>
              <span className="font-bold text-xl gradient-text">DanceStudio</span>
            </div>
            <p className="text-gray-300 text-sm">
              Unleash your rhythm and discover the joy of dance in the heart of Biratnagar. 
              Join our vibrant community of dancers today!
            </p>
            <div className="flex space-x-4">
              <Link href="#" className="text-gray-300 hover:text-primary transition-colors">
                <Facebook className="h-5 w-5" />
              </Link>
              <Link href="#" className="text-gray-300 hover:text-primary transition-colors">
                <Instagram className="h-5 w-5" />
              </Link>
              <Link href="#" className="text-gray-300 hover:text-primary transition-colors">
                <Youtube className="h-5 w-5" />
              </Link>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Quick Links</h3>
            <ul className="space-y-2">
              <li><Link href="/about" className="text-gray-300 hover:text-primary transition-colors">About Us</Link></li>
              <li><Link href="/classes" className="text-gray-300 hover:text-primary transition-colors">Dance Classes</Link></li>
              <li><Link href="/instructors" className="text-gray-300 hover:text-primary transition-colors">Instructors</Link></li>
              <li><Link href="/gallery" className="text-gray-300 hover:text-primary transition-colors">Gallery</Link></li>
              <li><Link href="/packages" className="text-gray-300 hover:text-primary transition-colors">Packages</Link></li>
            </ul>
          </div>

          {/* Dance Styles */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Dance Styles</h3>
            <ul className="space-y-2">
              <li className="text-gray-300">Hip-Hop</li>
              <li className="text-gray-300">Contemporary</li>
              <li className="text-gray-300">Classical</li>
              <li className="text-gray-300">Zumba</li>
              <li className="text-gray-300">Kids Classes</li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Contact Info</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <MapPin className="h-4 w-4 text-primary" />
                <span className="text-gray-300 text-sm">Main Road, Biratnagar, Nepal</span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-4 w-4 text-primary" />
                <span className="text-gray-300 text-sm">+977-9800000000</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-primary" />
                <span className="text-gray-300 text-sm"><EMAIL></span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-8 pt-8 text-center">
          <p className="text-gray-300 text-sm">
            © 2024 DanceStudio Biratnagar. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
