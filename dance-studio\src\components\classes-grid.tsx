"use client";

import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>, Star, Calendar, Mic, Zap, Drama, Dumbbell, Baby } from "lucide-react";
import { useDanceClasses } from "@/lib/queries";
import { useState } from "react";

const categories = ["All", "Hip-Hop", "Contemporary", "Classical", "Zumba", "Kids"];

export function ClassesGrid() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [selectedCategory, setSelectedCategory] = useState("All");
  const { data: classes, isLoading } = useDanceClasses();

  const filteredClasses = classes?.filter(
    (danceClass) => selectedCategory === "All" || danceClass.category === selectedCategory
  ) || [];

  if (isLoading) {
    return (
      <section className="section-padding bg-background">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
              All <span className="gradient-text">Classes</span>
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="dance-card animate-pulse">
                <div className="h-48 bg-muted rounded-lg mb-4" />
                <div className="h-4 bg-muted rounded mb-2" />
                <div className="h-3 bg-muted rounded mb-4" />
                <div className="h-8 bg-muted rounded" />
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="section-padding bg-background" ref={ref}>
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
            All <span className="gradient-text">Classes</span>
          </h2>
          <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            Choose from our comprehensive selection of dance classes, each designed to 
            help you grow as a dancer while having fun.
          </p>

          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-2 mb-12">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                onClick={() => setSelectedCategory(category)}
                className="mb-2"
              >
                {category}
              </Button>
            ))}
          </div>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredClasses.map((danceClass, index) => (
            <motion.div
              key={danceClass.id}
              initial={{ opacity: 0, y: 50 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: index * 0.1 }}
            >
              <Card className="dance-card h-full">
                <div className="relative h-48 bg-gradient-to-br from-primary/20 to-purple-500/20 rounded-t-lg flex items-center justify-center">
                  <div className="opacity-20">
                    {danceClass.category === "Hip-Hop" && <Mic className="w-16 h-16 text-primary" />}
                    {danceClass.category === "Contemporary" && <Zap className="w-16 h-16 text-primary" />}
                    {danceClass.category === "Classical" && <Drama className="w-16 h-16 text-primary" />}
                    {danceClass.category === "Zumba" && <Dumbbell className="w-16 h-16 text-primary" />}
                    {danceClass.category === "Kids" && <Baby className="w-16 h-16 text-primary" />}
                  </div>
                  <Badge className="absolute top-4 right-4 bg-primary">
                    {danceClass.category}
                  </Badge>
                  <Badge 
                    variant="secondary" 
                    className="absolute top-4 left-4"
                  >
                    {danceClass.level}
                  </Badge>
                </div>
                <CardHeader>
                  <CardTitle className="text-xl">{danceClass.name}</CardTitle>
                  <CardDescription>{danceClass.description}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>{danceClass.duration}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="w-4 h-4" />
                      <span>{danceClass.ageGroup}</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center space-x-1 text-sm font-semibold">
                      <Calendar className="w-4 h-4" />
                      <span>Schedule:</span>
                    </div>
                    {danceClass.schedule.map((schedule, idx) => (
                      <div key={idx} className="text-sm text-muted-foreground pl-5">
                        <strong>{schedule.day}:</strong> {schedule.time}
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex gap-2 pt-4">
                    <Button className="flex-1 bg-primary hover:bg-primary/90">
                      Join Class
                    </Button>
                    <Button variant="outline" className="flex-1">
                      Try Free
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {filteredClasses.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <p className="text-lg text-muted-foreground">
              No classes found for the selected category.
            </p>
          </motion.div>
        )}
      </div>
    </section>
  );
}
