# DanceStudio Biratnagar - Modern Dance Studio Website

A modern, responsive website for DanceStudio Biratnagar built with Next.js 15, TypeScript, Tailwind CSS, and ShadCN UI.

## 🎯 Features

- **Modern Design**: Clean, visually engaging interface with energetic colors
- **Fully Responsive**: Optimized for all devices and screen sizes
- **SEO Optimized**: Structured data, sitemap, and meta tags for better search visibility
- **Interactive Components**: Smooth animations and transitions using Framer Motion
- **Mock Data Integration**: TanStack Query for data fetching with realistic mock data
- **Comprehensive Pages**: Home, About, Classes, Instructors, Gallery, Packages, Contact, and FAQ sections

## 🏗️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: ShadCN UI
- **Animations**: Framer Motion
- **Data Fetching**: TanStack Query
- **Icons**: Lucide React

## 📱 Pages & Sections

### Home Page
- Hero section with animated background
- Studio introduction
- Featured classes showcase
- Student testimonials carousel
- FAQ section

### About Page
- Mission, vision, and values
- Studio story and history
- "Why choose us" highlights
- Founder profiles

### Classes Page
- Complete class listings with filtering
- Detailed class information
- Weekly schedule view
- Class categories: Hip-Hop, Contemporary, Classical, Zumba, Kids

### Instructors Page
- Instructor profiles with specialties
- Experience and achievements
- Team statistics

### Gallery Page
- Photo grid with lightbox viewer
- Category filtering (Performances, Classes, Events)
- Interactive image navigation

### Packages Page
- Pricing cards for different plans
- Feature comparisons
- Package-specific FAQ

### Contact Page
- Contact form with validation
- Studio information and hours
- Location details and directions
- Social media links

## 🚀 Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd dance-studio
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🎨 Design Features

- **Color Scheme**: Black, vibrant pink/purple (#e91e63), and white
- **Typography**: Modern, clean fonts with proper hierarchy
- **Animations**: Smooth page transitions and hover effects
- **Cards**: Elevated design with hover animations
- **Responsive Grid**: Adaptive layouts for all screen sizes

## 📊 Mock Data

The website includes comprehensive mock data for:
- 5 different dance classes with schedules
- 4 instructor profiles
- Student testimonials
- 3 pricing packages
- Gallery images with categories

## 📈 SEO Features

- Structured data for local business
- Optimized meta tags for all pages
- Sitemap generation
- Robots.txt configuration
- Open Graph and Twitter Card support

## 🚀 Deployment

The website is ready for deployment on platforms like:
- Vercel (recommended for Next.js)
- Netlify
- AWS Amplify
- Any hosting service supporting Node.js

Built with ❤️ for the dance community in Biratnagar, Nepal.
