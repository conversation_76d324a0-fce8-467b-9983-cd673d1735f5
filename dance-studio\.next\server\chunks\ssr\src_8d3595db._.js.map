{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/dance/dance-studio/src/components/hero-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { motion } from \"framer-motion\";\nimport Link from \"next/link\";\n\nexport function HeroSection() {\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background */}\n      <div className=\"absolute inset-0 hero-gradient\" />\n      \n      {/* Animated background elements */}\n      <div className=\"absolute inset-0\">\n        <motion.div\n          className=\"absolute top-20 left-10 w-32 h-32 bg-primary/20 rounded-full blur-xl\"\n          animate={{\n            x: [0, 100, 0],\n            y: [0, -50, 0],\n          }}\n          transition={{\n            duration: 8,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n          }}\n        />\n        <motion.div\n          className=\"absolute bottom-20 right-10 w-48 h-48 bg-purple-500/20 rounded-full blur-xl\"\n          animate={{\n            x: [0, -80, 0],\n            y: [0, 60, 0],\n          }}\n          transition={{\n            duration: 10,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n          }}\n        />\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n        >\n          <h1 className=\"text-4xl sm:text-6xl lg:text-7xl font-bold text-white mb-6\">\n            Unleash Your{\" \"}\n            <span className=\"gradient-text\">Rhythm</span>\n          </h1>\n          <h2 className=\"text-xl sm:text-2xl lg:text-3xl text-gray-300 mb-8\">\n            Dance with Us in Biratnagar\n          </h2>\n          <p className=\"text-lg sm:text-xl text-gray-400 mb-12 max-w-3xl mx-auto\">\n            Discover the joy of movement at Biratnagar's premier dance studio. \n            From hip-hop to classical, contemporary to Zumba - find your passion and join our vibrant community.\n          </p>\n          \n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n            <Link href=\"/classes\">\n              <Button size=\"lg\" className=\"bg-primary hover:bg-primary/90 text-white px-8 py-4 text-lg\">\n                Explore Classes\n              </Button>\n            </Link>\n            <Link href=\"/contact\">\n              <Button \n                variant=\"outline\" \n                size=\"lg\" \n                className=\"border-white text-white hover:bg-white hover:text-black px-8 py-4 text-lg\"\n              >\n                Book Trial Class\n              </Button>\n            </Link>\n          </div>\n        </motion.div>\n\n        {/* Scroll indicator */}\n        <motion.div\n          className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n        >\n          <div className=\"w-6 h-10 border-2 border-white/50 rounded-full flex justify-center\">\n            <div className=\"w-1 h-3 bg-white/50 rounded-full mt-2\" />\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG;gCAAK;6BAAE;4BACd,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;wBAChB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAEF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;4BACd,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBACf;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,8OAAC;gCAAG,WAAU;;oCAA6D;oCAC5D;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,8OAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAGnE,8OAAC;gCAAE,WAAU;0CAA2D;;;;;;0CAKxE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAA8D;;;;;;;;;;;kDAI5F,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;kCAQP,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBAAC;wBACzB,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;kCAE5C,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B", "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/dance/dance-studio/src/components/intro-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { Heart, Users, Trophy, Music } from \"lucide-react\";\n\nconst features = [\n  {\n    icon: Heart,\n    title: \"Passion for Dance\",\n    description: \"We believe dance is a universal language that connects hearts and souls.\"\n  },\n  {\n    icon: Users,\n    title: \"Community Spirit\",\n    description: \"Join a supportive community of dancers who encourage and inspire each other.\"\n  },\n  {\n    icon: Trophy,\n    title: \"Excellence\",\n    description: \"Our experienced instructors are dedicated to helping you achieve your best.\"\n  },\n  {\n    icon: Music,\n    title: \"All Styles\",\n    description: \"From traditional to contemporary, we offer diverse dance styles for everyone.\"\n  }\n];\n\nexport function IntroSection() {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  return (\n    <section className=\"section-padding bg-background\" ref={ref}>\n      <div className=\"max-w-7xl mx-auto\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            Welcome to <span className=\"gradient-text\">DanceStudio</span>\n          </h2>\n          <p className=\"text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto\">\n            Located in the heart of Biratnagar, we are Nepal's premier dance destination \n            where creativity meets technique, and passion meets performance. Our studio \n            is more than just a place to learn dance – it's where dreams take flight.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {features.map((feature, index) => (\n            <motion.div\n              key={feature.title}\n              initial={{ opacity: 0, y: 50 }}\n              animate={inView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.8, delay: index * 0.2 }}\n              className=\"text-center\"\n            >\n              <div className=\"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <feature.icon className=\"w-8 h-8 text-primary\" />\n              </div>\n              <h3 className=\"text-xl font-semibold mb-3\">{feature.title}</h3>\n              <p className=\"text-muted-foreground\">{feature.description}</p>\n            </motion.div>\n          ))}\n        </div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.8 }}\n          className=\"mt-16 text-center\"\n        >\n          <div className=\"bg-card border border-border rounded-lg p-8 max-w-4xl mx-auto\">\n            <h3 className=\"text-2xl font-bold mb-4\">Our Mission</h3>\n            <p className=\"text-lg text-muted-foreground\">\n              To create a vibrant dance community in Biratnagar where people of all ages and backgrounds \n              can discover the joy of movement, build confidence, stay fit, and express their creativity \n              through the art of dance. We are committed to providing high-quality instruction in a \n              supportive and inclusive environment.\n            </p>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,WAAW;IACf;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;CACD;AAEM,SAAS;IACd,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,qBACE,8OAAC;QAAQ,WAAU;QAAgC,KAAK;kBACtD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;;gCAAkD;8CACnD,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAE7C,8OAAC;4BAAE,WAAU;sCAA6D;;;;;;;;;;;;8BAO5E,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,QAAQ,IAAI;wCAAC,WAAU;;;;;;;;;;;8CAE1B,8OAAC;oCAAG,WAAU;8CAA8B,QAAQ,KAAK;;;;;;8CACzD,8OAAC;oCAAE,WAAU;8CAAyB,QAAQ,WAAW;;;;;;;2BAVpD,QAAQ,KAAK;;;;;;;;;;8BAexB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWzD", "debugId": null}}, {"offset": {"line": 445, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/dance/dance-studio/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 540, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/dance/dance-studio/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 584, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/dance/dance-studio/src/lib/data.ts"], "sourcesContent": ["// Mock data for the dance studio\n\nexport interface DanceClass {\n  id: string;\n  name: string;\n  description: string;\n  category: string;\n  ageGroup: string;\n  duration: string;\n  level: string;\n  image: string;\n  schedule: {\n    day: string;\n    time: string;\n  }[];\n}\n\nexport interface Instructor {\n  id: string;\n  name: string;\n  bio: string;\n  specialties: string[];\n  experience: string;\n  image: string;\n}\n\nexport interface Testimonial {\n  id: string;\n  name: string;\n  role: string;\n  content: string;\n  rating: number;\n  image: string;\n}\n\nexport interface Package {\n  id: string;\n  name: string;\n  description: string;\n  price: number;\n  duration: string;\n  features: string[];\n  popular: boolean;\n}\n\nexport const danceClasses: DanceClass[] = [\n  {\n    id: \"1\",\n    name: \"Hip-Hop Fundamentals\",\n    description: \"Learn the basics of hip-hop dance with energetic moves and urban style.\",\n    category: \"Hip-Hop\",\n    ageGroup: \"13+\",\n    duration: \"60 minutes\",\n    level: \"Beginner\",\n    image: \"/images/hip-hop.jpg\",\n    schedule: [\n      { day: \"Monday\", time: \"6:00 PM - 7:00 PM\" },\n      { day: \"Wednesday\", time: \"6:00 PM - 7:00 PM\" },\n      { day: \"Friday\", time: \"6:00 PM - 7:00 PM\" }\n    ]\n  },\n  {\n    id: \"2\",\n    name: \"Contemporary Flow\",\n    description: \"Express yourself through fluid movements and emotional storytelling.\",\n    category: \"Contemporary\",\n    ageGroup: \"16+\",\n    duration: \"75 minutes\",\n    level: \"Intermediate\",\n    image: \"/images/contemporary.jpg\",\n    schedule: [\n      { day: \"Tuesday\", time: \"7:00 PM - 8:15 PM\" },\n      { day: \"Thursday\", time: \"7:00 PM - 8:15 PM\" }\n    ]\n  },\n  {\n    id: \"3\",\n    name: \"Classical Dance\",\n    description: \"Traditional Nepali and Indian classical dance forms with grace and precision.\",\n    category: \"Classical\",\n    ageGroup: \"8+\",\n    duration: \"90 minutes\",\n    level: \"All Levels\",\n    image: \"/images/classical.jpg\",\n    schedule: [\n      { day: \"Saturday\", time: \"10:00 AM - 11:30 AM\" },\n      { day: \"Sunday\", time: \"10:00 AM - 11:30 AM\" }\n    ]\n  },\n  {\n    id: \"4\",\n    name: \"Zumba Fitness\",\n    description: \"High-energy dance fitness combining Latin rhythms with easy-to-follow moves.\",\n    category: \"Zumba\",\n    ageGroup: \"18+\",\n    duration: \"45 minutes\",\n    level: \"All Levels\",\n    image: \"/images/zumba.jpg\",\n    schedule: [\n      { day: \"Monday\", time: \"7:00 AM - 7:45 AM\" },\n      { day: \"Wednesday\", time: \"7:00 AM - 7:45 AM\" },\n      { day: \"Friday\", time: \"7:00 AM - 7:45 AM\" }\n    ]\n  },\n  {\n    id: \"5\",\n    name: \"Kids Dance Party\",\n    description: \"Fun and creative dance classes designed specifically for children.\",\n    category: \"Kids\",\n    ageGroup: \"5-12\",\n    duration: \"45 minutes\",\n    level: \"Beginner\",\n    image: \"/images/kids-dance.jpg\",\n    schedule: [\n      { day: \"Saturday\", time: \"2:00 PM - 2:45 PM\" },\n      { day: \"Sunday\", time: \"2:00 PM - 2:45 PM\" }\n    ]\n  }\n];\n\nexport const instructors: Instructor[] = [\n  {\n    id: \"1\",\n    name: \"Priya Sharma\",\n    bio: \"Lead instructor with over 8 years of experience in contemporary and classical dance.\",\n    specialties: [\"Contemporary\", \"Classical\", \"Choreography\"],\n    experience: \"8+ years\",\n    image: \"/images/instructor-1.jpg\"\n  },\n  {\n    id: \"2\",\n    name: \"Raj Thapa\",\n    bio: \"Hip-hop specialist who brings street dance culture to Biratnagar with authentic moves.\",\n    specialties: [\"Hip-Hop\", \"Breaking\", \"Urban Dance\"],\n    experience: \"6+ years\",\n    image: \"/images/instructor-2.jpg\"\n  },\n  {\n    id: \"3\",\n    name: \"Maya Gurung\",\n    bio: \"Certified Zumba instructor passionate about fitness and dance fusion.\",\n    specialties: [\"Zumba\", \"Dance Fitness\", \"Latin Dance\"],\n    experience: \"5+ years\",\n    image: \"/images/instructor-3.jpg\"\n  },\n  {\n    id: \"4\",\n    name: \"Arjun Rai\",\n    bio: \"Kids dance specialist who makes learning fun and engaging for young dancers.\",\n    specialties: [\"Kids Dance\", \"Creative Movement\", \"Musical Theatre\"],\n    experience: \"4+ years\",\n    image: \"/images/instructor-4.jpg\"\n  }\n];\n\nexport const testimonials: Testimonial[] = [\n  {\n    id: \"1\",\n    name: \"Sita Poudel\",\n    role: \"Student\",\n    content: \"DanceStudio has transformed my confidence and fitness. The instructors are amazing and the community is so welcoming!\",\n    rating: 5,\n    image: \"/images/testimonial-1.jpg\"\n  },\n  {\n    id: \"2\",\n    name: \"Ramesh Shrestha\",\n    role: \"Parent\",\n    content: \"My daughter loves her kids dance classes here. She's learned so much and made great friends. Highly recommend!\",\n    rating: 5,\n    image: \"/images/testimonial-2.jpg\"\n  },\n  {\n    id: \"3\",\n    name: \"Anita Karki\",\n    role: \"Student\",\n    content: \"The contemporary classes are incredible. I've grown so much as a dancer and performer. Thank you DanceStudio!\",\n    rating: 5,\n    image: \"/images/testimonial-3.jpg\"\n  }\n];\n\nexport const packages: Package[] = [\n  {\n    id: \"1\",\n    name: \"Drop-In Class\",\n    description: \"Perfect for trying out classes or occasional visits\",\n    price: 500,\n    duration: \"Single Class\",\n    features: [\n      \"Access to any single class\",\n      \"No commitment required\",\n      \"Great for beginners\"\n    ],\n    popular: false\n  },\n  {\n    id: \"2\",\n    name: \"Weekly Package\",\n    description: \"Ideal for regular practice and skill development\",\n    price: 1800,\n    duration: \"1 Week\",\n    features: [\n      \"Unlimited classes for 1 week\",\n      \"Access to all dance styles\",\n      \"Priority booking\",\n      \"Free water bottle\"\n    ],\n    popular: true\n  },\n  {\n    id: \"3\",\n    name: \"Monthly Unlimited\",\n    description: \"Best value for dedicated dancers\",\n    price: 6000,\n    duration: \"1 Month\",\n    features: [\n      \"Unlimited classes for 1 month\",\n      \"Access to all dance styles\",\n      \"Priority booking\",\n      \"Free merchandise\",\n      \"Guest pass (1 per month)\"\n    ],\n    popular: false\n  }\n];\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;;AA6C1B,MAAM,eAA6B;IACxC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,OAAO;QACP,OAAO;QACP,UAAU;YACR;gBAAE,KAAK;gBAAU,MAAM;YAAoB;YAC3C;gBAAE,KAAK;gBAAa,MAAM;YAAoB;YAC9C;gBAAE,KAAK;gBAAU,MAAM;YAAoB;SAC5C;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,OAAO;QACP,OAAO;QACP,UAAU;YACR;gBAAE,KAAK;gBAAW,MAAM;YAAoB;YAC5C;gBAAE,KAAK;gBAAY,MAAM;YAAoB;SAC9C;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,OAAO;QACP,OAAO;QACP,UAAU;YACR;gBAAE,KAAK;gBAAY,MAAM;YAAsB;YAC/C;gBAAE,KAAK;gBAAU,MAAM;YAAsB;SAC9C;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,OAAO;QACP,OAAO;QACP,UAAU;YACR;gBAAE,KAAK;gBAAU,MAAM;YAAoB;YAC3C;gBAAE,KAAK;gBAAa,MAAM;YAAoB;YAC9C;gBAAE,KAAK;gBAAU,MAAM;YAAoB;SAC5C;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,OAAO;QACP,OAAO;QACP,UAAU;YACR;gBAAE,KAAK;gBAAY,MAAM;YAAoB;YAC7C;gBAAE,KAAK;gBAAU,MAAM;YAAoB;SAC5C;IACH;CACD;AAEM,MAAM,cAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,aAAa;YAAC;YAAgB;YAAa;SAAe;QAC1D,YAAY;QACZ,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,aAAa;YAAC;YAAW;YAAY;SAAc;QACnD,YAAY;QACZ,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,aAAa;YAAC;YAAS;YAAiB;SAAc;QACtD,YAAY;QACZ,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,aAAa;YAAC;YAAc;YAAqB;SAAkB;QACnE,YAAY;QACZ,OAAO;IACT;CACD;AAEM,MAAM,eAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,OAAO;IACT;CACD;AAEM,MAAM,WAAsB;IACjC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU;QACV,UAAU;YACR;YACA;YACA;SACD;QACD,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU;QACV,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU;QACV,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;IACX;CACD", "debugId": null}}, {"offset": {"line": 825, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/dance/dance-studio/src/lib/queries.ts"], "sourcesContent": ["import { useQuery } from '@tanstack/react-query';\nimport { danceClasses, instructors, testimonials, packages } from './data';\n\n// Simulate API delay\nconst delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));\n\n// Mock API functions\nexport const fetchDanceClasses = async () => {\n  await delay(500);\n  return danceClasses;\n};\n\nexport const fetchInstructors = async () => {\n  await delay(500);\n  return instructors;\n};\n\nexport const fetchTestimonials = async () => {\n  await delay(500);\n  return testimonials;\n};\n\nexport const fetchPackages = async () => {\n  await delay(500);\n  return packages;\n};\n\n// React Query hooks\nexport const useDanceClasses = () => {\n  return useQuery({\n    queryKey: ['danceClasses'],\n    queryFn: fetchDanceClasses,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n};\n\nexport const useInstructors = () => {\n  return useQuery({\n    queryKey: ['instructors'],\n    queryFn: fetchInstructors,\n    staleTime: 5 * 60 * 1000,\n  });\n};\n\nexport const useTestimonials = () => {\n  return useQuery({\n    queryKey: ['testimonials'],\n    queryFn: fetchTestimonials,\n    staleTime: 5 * 60 * 1000,\n  });\n};\n\nexport const usePackages = () => {\n  return useQuery({\n    queryKey: ['packages'],\n    queryFn: fetchPackages,\n    staleTime: 5 * 60 * 1000,\n  });\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEA,qBAAqB;AACrB,MAAM,QAAQ,CAAC,KAAe,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AAGlE,MAAM,oBAAoB;IAC/B,MAAM,MAAM;IACZ,OAAO,kHAAA,CAAA,eAAY;AACrB;AAEO,MAAM,mBAAmB;IAC9B,MAAM,MAAM;IACZ,OAAO,kHAAA,CAAA,cAAW;AACpB;AAEO,MAAM,oBAAoB;IAC/B,MAAM,MAAM;IACZ,OAAO,kHAAA,CAAA,eAAY;AACrB;AAEO,MAAM,gBAAgB;IAC3B,MAAM,MAAM;IACZ,OAAO,kHAAA,CAAA,WAAQ;AACjB;AAGO,MAAM,kBAAkB;IAC7B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAe;QAC1B,SAAS;QACT,WAAW,IAAI,KAAK;IACtB;AACF;AAEO,MAAM,iBAAiB;IAC5B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAc;QACzB,SAAS;QACT,WAAW,IAAI,KAAK;IACtB;AACF;AAEO,MAAM,kBAAkB;IAC7B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAe;QAC1B,SAAS;QACT,WAAW,IAAI,KAAK;IACtB;AACF;AAEO,MAAM,cAAc;IACzB,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAW;QACtB,SAAS;QACT,WAAW,IAAI,KAAK;IACtB;AACF", "debugId": null}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/dance/dance-studio/src/components/featured-classes.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Clock, Users, Star, Music } from \"lucide-react\";\nimport Link from \"next/link\";\nimport { useDanceClasses } from \"@/lib/queries\";\n\nexport function FeaturedClasses() {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const { data: classes, isLoading } = useDanceClasses();\n  const featuredClasses = classes?.slice(0, 3) || [];\n\n  if (isLoading) {\n    return (\n      <section className=\"section-padding bg-muted/50\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n              Featured <span className=\"gradient-text\">Classes</span>\n            </h2>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {[1, 2, 3].map((i) => (\n              <div key={i} className=\"dance-card animate-pulse\">\n                <div className=\"h-48 bg-muted rounded-lg mb-4\" />\n                <div className=\"h-4 bg-muted rounded mb-2\" />\n                <div className=\"h-3 bg-muted rounded mb-4\" />\n                <div className=\"h-8 bg-muted rounded\" />\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section className=\"section-padding bg-muted/50\" ref={ref}>\n      <div className=\"max-w-7xl mx-auto\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            Featured <span className=\"gradient-text\">Classes</span>\n          </h2>\n          <p className=\"text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto\">\n            Discover our most popular dance classes designed for all skill levels. \n            Each class is crafted to help you grow as a dancer while having fun.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {featuredClasses.map((danceClass, index) => (\n            <motion.div\n              key={danceClass.id}\n              initial={{ opacity: 0, y: 50 }}\n              animate={inView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.8, delay: index * 0.2 }}\n            >\n              <Card className=\"dance-card h-full\">\n                <div className=\"relative h-48 bg-gradient-to-br from-primary/20 to-purple-500/20 rounded-t-lg flex items-center justify-center\">\n                  <Music className=\"w-16 h-16 text-primary opacity-20\" />\n                  <Badge className=\"absolute top-4 right-4 bg-primary\">\n                    {danceClass.category}\n                  </Badge>\n                </div>\n                <CardHeader>\n                  <CardTitle className=\"text-xl\">{danceClass.name}</CardTitle>\n                  <CardDescription>{danceClass.description}</CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"flex items-center justify-between text-sm text-muted-foreground\">\n                    <div className=\"flex items-center space-x-1\">\n                      <Clock className=\"w-4 h-4\" />\n                      <span>{danceClass.duration}</span>\n                    </div>\n                    <div className=\"flex items-center space-x-1\">\n                      <Users className=\"w-4 h-4\" />\n                      <span>{danceClass.ageGroup}</span>\n                    </div>\n                    <div className=\"flex items-center space-x-1\">\n                      <Star className=\"w-4 h-4\" />\n                      <span>{danceClass.level}</span>\n                    </div>\n                  </div>\n                  \n                  <div className=\"space-y-2\">\n                    <h4 className=\"font-semibold text-sm\">Schedule:</h4>\n                    {danceClass.schedule.slice(0, 2).map((schedule, idx) => (\n                      <div key={idx} className=\"text-sm text-muted-foreground\">\n                        {schedule.day}: {schedule.time}\n                      </div>\n                    ))}\n                  </div>\n                  \n                  <Button className=\"w-full bg-primary hover:bg-primary/90\">\n                    Join This Class\n                  </Button>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          className=\"text-center mt-12\"\n        >\n          <Link href=\"/classes\">\n            <Button variant=\"outline\" size=\"lg\" className=\"px-8 py-4\">\n              View All Classes\n            </Button>\n          </Link>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AATA;;;;;;;;;;AAWO,SAAS;IACd,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,EAAE,MAAM,OAAO,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD;IACnD,MAAM,kBAAkB,SAAS,MAAM,GAAG,MAAM,EAAE;IAElD,IAAI,WAAW;QACb,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;;gCAAkD;8CACrD,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAG7C,8OAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;gCAAY,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;+BAJP;;;;;;;;;;;;;;;;;;;;;IAWtB;IAEA,qBACE,8OAAC;QAAQ,WAAU;QAA8B,KAAK;kBACpD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;;gCAAkD;8CACrD,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAE3C,8OAAC;4BAAE,WAAU;sCAA6D;;;;;;;;;;;;8BAM5E,8OAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC,YAAY,sBAChC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;sCAEhD,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;0DACd,WAAW,QAAQ;;;;;;;;;;;;kDAGxB,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAW,WAAW,IAAI;;;;;;0DAC/C,8OAAC,gIAAA,CAAA,kBAAe;0DAAE,WAAW,WAAW;;;;;;;;;;;;kDAE1C,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAM,WAAW,QAAQ;;;;;;;;;;;;kEAE5B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAM,WAAW,QAAQ;;;;;;;;;;;;kEAE5B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAM,WAAW,KAAK;;;;;;;;;;;;;;;;;;0DAI3B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;oDACrC,WAAW,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,UAAU,oBAC9C,8OAAC;4DAAc,WAAU;;gEACtB,SAAS,GAAG;gEAAC;gEAAG,SAAS,IAAI;;2DADtB;;;;;;;;;;;0DAMd,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;0DAAwC;;;;;;;;;;;;;;;;;;2BAzCzD,WAAW,EAAE;;;;;;;;;;8BAkDxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;4BAAK,WAAU;sCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtE", "debugId": null}}, {"offset": {"line": 1329, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/dance/dance-studio/src/components/ui/carousel.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport useEmblaCarousel, {\n  type UseEmblaCarouselType,\n} from \"embla-carousel-react\"\nimport { ArrowLeft, ArrowRight } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\nimport { But<PERSON> } from \"@/components/ui/button\"\n\ntype CarouselApi = UseEmblaCarouselType[1]\ntype UseCarouselParameters = Parameters<typeof useEmblaCarousel>\ntype CarouselOptions = UseCarouselParameters[0]\ntype CarouselPlugin = UseCarouselParameters[1]\n\ntype CarouselProps = {\n  opts?: CarouselOptions\n  plugins?: CarouselPlugin\n  orientation?: \"horizontal\" | \"vertical\"\n  setApi?: (api: CarouselApi) => void\n}\n\ntype CarouselContextProps = {\n  carouselRef: ReturnType<typeof useEmblaCarousel>[0]\n  api: ReturnType<typeof useEmblaCarousel>[1]\n  scrollPrev: () => void\n  scrollNext: () => void\n  canScrollPrev: boolean\n  canScrollNext: boolean\n} & CarouselProps\n\nconst CarouselContext = React.createContext<CarouselContextProps | null>(null)\n\nfunction useCarousel() {\n  const context = React.useContext(CarouselContext)\n\n  if (!context) {\n    throw new Error(\"useCarousel must be used within a <Carousel />\")\n  }\n\n  return context\n}\n\nfunction Carousel({\n  orientation = \"horizontal\",\n  opts,\n  setApi,\n  plugins,\n  className,\n  children,\n  ...props\n}: React.ComponentProps<\"div\"> & CarouselProps) {\n  const [carouselRef, api] = useEmblaCarousel(\n    {\n      ...opts,\n      axis: orientation === \"horizontal\" ? \"x\" : \"y\",\n    },\n    plugins\n  )\n  const [canScrollPrev, setCanScrollPrev] = React.useState(false)\n  const [canScrollNext, setCanScrollNext] = React.useState(false)\n\n  const onSelect = React.useCallback((api: CarouselApi) => {\n    if (!api) return\n    setCanScrollPrev(api.canScrollPrev())\n    setCanScrollNext(api.canScrollNext())\n  }, [])\n\n  const scrollPrev = React.useCallback(() => {\n    api?.scrollPrev()\n  }, [api])\n\n  const scrollNext = React.useCallback(() => {\n    api?.scrollNext()\n  }, [api])\n\n  const handleKeyDown = React.useCallback(\n    (event: React.KeyboardEvent<HTMLDivElement>) => {\n      if (event.key === \"ArrowLeft\") {\n        event.preventDefault()\n        scrollPrev()\n      } else if (event.key === \"ArrowRight\") {\n        event.preventDefault()\n        scrollNext()\n      }\n    },\n    [scrollPrev, scrollNext]\n  )\n\n  React.useEffect(() => {\n    if (!api || !setApi) return\n    setApi(api)\n  }, [api, setApi])\n\n  React.useEffect(() => {\n    if (!api) return\n    onSelect(api)\n    api.on(\"reInit\", onSelect)\n    api.on(\"select\", onSelect)\n\n    return () => {\n      api?.off(\"select\", onSelect)\n    }\n  }, [api, onSelect])\n\n  return (\n    <CarouselContext.Provider\n      value={{\n        carouselRef,\n        api: api,\n        opts,\n        orientation:\n          orientation || (opts?.axis === \"y\" ? \"vertical\" : \"horizontal\"),\n        scrollPrev,\n        scrollNext,\n        canScrollPrev,\n        canScrollNext,\n      }}\n    >\n      <div\n        onKeyDownCapture={handleKeyDown}\n        className={cn(\"relative\", className)}\n        role=\"region\"\n        aria-roledescription=\"carousel\"\n        data-slot=\"carousel\"\n        {...props}\n      >\n        {children}\n      </div>\n    </CarouselContext.Provider>\n  )\n}\n\nfunction CarouselContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  const { carouselRef, orientation } = useCarousel()\n\n  return (\n    <div\n      ref={carouselRef}\n      className=\"overflow-hidden\"\n      data-slot=\"carousel-content\"\n    >\n      <div\n        className={cn(\n          \"flex\",\n          orientation === \"horizontal\" ? \"-ml-4\" : \"-mt-4 flex-col\",\n          className\n        )}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction CarouselItem({ className, ...props }: React.ComponentProps<\"div\">) {\n  const { orientation } = useCarousel()\n\n  return (\n    <div\n      role=\"group\"\n      aria-roledescription=\"slide\"\n      data-slot=\"carousel-item\"\n      className={cn(\n        \"min-w-0 shrink-0 grow-0 basis-full\",\n        orientation === \"horizontal\" ? \"pl-4\" : \"pt-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CarouselPrevious({\n  className,\n  variant = \"outline\",\n  size = \"icon\",\n  ...props\n}: React.ComponentProps<typeof Button>) {\n  const { orientation, scrollPrev, canScrollPrev } = useCarousel()\n\n  return (\n    <Button\n      data-slot=\"carousel-previous\"\n      variant={variant}\n      size={size}\n      className={cn(\n        \"absolute size-8 rounded-full\",\n        orientation === \"horizontal\"\n          ? \"top-1/2 -left-12 -translate-y-1/2\"\n          : \"-top-12 left-1/2 -translate-x-1/2 rotate-90\",\n        className\n      )}\n      disabled={!canScrollPrev}\n      onClick={scrollPrev}\n      {...props}\n    >\n      <ArrowLeft />\n      <span className=\"sr-only\">Previous slide</span>\n    </Button>\n  )\n}\n\nfunction CarouselNext({\n  className,\n  variant = \"outline\",\n  size = \"icon\",\n  ...props\n}: React.ComponentProps<typeof Button>) {\n  const { orientation, scrollNext, canScrollNext } = useCarousel()\n\n  return (\n    <Button\n      data-slot=\"carousel-next\"\n      variant={variant}\n      size={size}\n      className={cn(\n        \"absolute size-8 rounded-full\",\n        orientation === \"horizontal\"\n          ? \"top-1/2 -right-12 -translate-y-1/2\"\n          : \"-bottom-12 left-1/2 -translate-x-1/2 rotate-90\",\n        className\n      )}\n      disabled={!canScrollNext}\n      onClick={scrollNext}\n      {...props}\n    >\n      <ArrowRight />\n      <span className=\"sr-only\">Next slide</span>\n    </Button>\n  )\n}\n\nexport {\n  type CarouselApi,\n  Carousel,\n  CarouselContent,\n  CarouselItem,\n  CarouselPrevious,\n  CarouselNext,\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AAGA;AAAA;AAEA;AACA;AATA;;;;;;;AAgCA,MAAM,gCAAkB,qMAAA,CAAA,gBAAmB,CAA8B;AAEzE,SAAS;IACP,MAAM,UAAU,qMAAA,CAAA,aAAgB,CAAC;IAEjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,SAAS,EAChB,cAAc,YAAY,EAC1B,IAAI,EACJ,MAAM,EACN,OAAO,EACP,SAAS,EACT,QAAQ,EACR,GAAG,OACyC;IAC5C,MAAM,CAAC,aAAa,IAAI,GAAG,CAAA,GAAA,sLAAA,CAAA,UAAgB,AAAD,EACxC;QACE,GAAG,IAAI;QACP,MAAM,gBAAgB,eAAe,MAAM;IAC7C,GACA;IAEF,MAAM,CAAC,eAAe,iBAAiB,GAAG,qMAAA,CAAA,WAAc,CAAC;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,qMAAA,CAAA,WAAc,CAAC;IAEzD,MAAM,WAAW,qMAAA,CAAA,cAAiB,CAAC,CAAC;QAClC,IAAI,CAAC,KAAK;QACV,iBAAiB,IAAI,aAAa;QAClC,iBAAiB,IAAI,aAAa;IACpC,GAAG,EAAE;IAEL,MAAM,aAAa,qMAAA,CAAA,cAAiB,CAAC;QACnC,KAAK;IACP,GAAG;QAAC;KAAI;IAER,MAAM,aAAa,qMAAA,CAAA,cAAiB,CAAC;QACnC,KAAK;IACP,GAAG;QAAC;KAAI;IAER,MAAM,gBAAgB,qMAAA,CAAA,cAAiB,CACrC,CAAC;QACC,IAAI,MAAM,GAAG,KAAK,aAAa;YAC7B,MAAM,cAAc;YACpB;QACF,OAAO,IAAI,MAAM,GAAG,KAAK,cAAc;YACrC,MAAM,cAAc;YACpB;QACF;IACF,GACA;QAAC;QAAY;KAAW;IAG1B,qMAAA,CAAA,YAAe,CAAC;QACd,IAAI,CAAC,OAAO,CAAC,QAAQ;QACrB,OAAO;IACT,GAAG;QAAC;QAAK;KAAO;IAEhB,qMAAA,CAAA,YAAe,CAAC;QACd,IAAI,CAAC,KAAK;QACV,SAAS;QACT,IAAI,EAAE,CAAC,UAAU;QACjB,IAAI,EAAE,CAAC,UAAU;QAEjB,OAAO;YACL,KAAK,IAAI,UAAU;QACrB;IACF,GAAG;QAAC;QAAK;KAAS;IAElB,qBACE,8OAAC,gBAAgB,QAAQ;QACvB,OAAO;YACL;YACA,KAAK;YACL;YACA,aACE,eAAe,CAAC,MAAM,SAAS,MAAM,aAAa,YAAY;YAChE;YACA;YACA;YACA;QACF;kBAEA,cAAA,8OAAC;YACC,kBAAkB;YAClB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;YAC1B,MAAK;YACL,wBAAqB;YACrB,aAAU;YACT,GAAG,KAAK;sBAER;;;;;;;;;;;AAIT;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;IAErC,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,aAAU;kBAEV,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,QACA,gBAAgB,eAAe,UAAU,kBACzC;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,MAAM,EAAE,WAAW,EAAE,GAAG;IAExB,qBACE,8OAAC;QACC,MAAK;QACL,wBAAqB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sCACA,gBAAgB,eAAe,SAAS,QACxC;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,MAAM,EACb,GAAG,OACiC;IACpC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEnD,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,aAAU;QACV,SAAS;QACT,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gCACA,gBAAgB,eACZ,sCACA,+CACJ;QAEF,UAAU,CAAC;QACX,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC,gNAAA,CAAA,YAAS;;;;;0BACV,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,MAAM,EACb,GAAG,OACiC;IACpC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEnD,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,aAAU;QACV,SAAS;QACT,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gCACA,gBAAgB,eACZ,uCACA,kDACJ;QAEF,UAAU,CAAC;QACX,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC,kNAAA,CAAA,aAAU;;;;;0BACX,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 1542, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/dance/dance-studio/src/components/testimonials-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Star } from \"lucide-react\";\nimport { useTestimonials } from \"@/lib/queries\";\nimport { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from \"@/components/ui/carousel\";\n\nexport function TestimonialsSection() {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const { data: testimonials, isLoading } = useTestimonials();\n\n  if (isLoading) {\n    return (\n      <section className=\"section-padding bg-background\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n              What Our <span className=\"gradient-text\">Students Say</span>\n            </h2>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {[1, 2, 3].map((i) => (\n              <div key={i} className=\"dance-card animate-pulse\">\n                <div className=\"h-32 bg-muted rounded mb-4\" />\n                <div className=\"h-4 bg-muted rounded mb-2\" />\n                <div className=\"h-3 bg-muted rounded\" />\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section className=\"section-padding bg-background\" ref={ref}>\n      <div className=\"max-w-7xl mx-auto\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            What Our <span className=\"gradient-text\">Students Say</span>\n          </h2>\n          <p className=\"text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto\">\n            Don't just take our word for it. Hear from our amazing community of dancers \n            who have found their passion and transformed their lives at DanceStudio.\n          </p>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          className=\"max-w-5xl mx-auto\"\n        >\n          <Carousel className=\"w-full\">\n            <CarouselContent>\n              {testimonials?.map((testimonial, index) => (\n                <CarouselItem key={testimonial.id} className=\"md:basis-1/2 lg:basis-1/3\">\n                  <Card className=\"dance-card h-full\">\n                    <CardContent className=\"p-6\">\n                      <div className=\"flex items-center mb-4\">\n                        {[...Array(testimonial.rating)].map((_, i) => (\n                          <Star key={i} className=\"w-4 h-4 fill-primary text-primary\" />\n                        ))}\n                      </div>\n                      \n                      <blockquote className=\"text-muted-foreground mb-6 italic\">\n                        \"{testimonial.content}\"\n                      </blockquote>\n                      \n                      <div className=\"flex items-center space-x-4\">\n                        <div className=\"w-12 h-12 bg-gradient-to-br from-primary/20 to-purple-500/20 rounded-full flex items-center justify-center\">\n                          <span className=\"text-lg font-semibold text-primary\">\n                            {testimonial.name.charAt(0)}\n                          </span>\n                        </div>\n                        <div>\n                          <div className=\"font-semibold\">{testimonial.name}</div>\n                          <div className=\"text-sm text-muted-foreground\">{testimonial.role}</div>\n                        </div>\n                      </div>\n                    </CardContent>\n                  </Card>\n                </CarouselItem>\n              ))}\n            </CarouselContent>\n            <CarouselPrevious />\n            <CarouselNext />\n          </Carousel>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"bg-primary/10 rounded-lg p-8 max-w-4xl mx-auto\">\n            <h3 className=\"text-2xl font-bold mb-4\">Ready to Start Your Dance Journey?</h3>\n            <p className=\"text-lg text-muted-foreground mb-6\">\n              Join hundreds of satisfied students who have discovered the joy of dance at our studio. \n              Book your trial class today and become part of our dance family!\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 rounded-lg font-semibold transition-colors\">\n                Book Trial Class\n              </button>\n              <button className=\"border border-primary text-primary hover:bg-primary hover:text-primary-foreground px-8 py-3 rounded-lg font-semibold transition-colors\">\n                Contact Us\n              </button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASO,SAAS;IACd,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,EAAE,MAAM,YAAY,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD;IAExD,IAAI,WAAW;QACb,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;;gCAAkD;8CACrD,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAG7C,8OAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;gCAAY,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;+BAHP;;;;;;;;;;;;;;;;;;;;;IAUtB;IAEA,qBACE,8OAAC;QAAQ,WAAU;QAAgC,KAAK;kBACtD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;;gCAAkD;8CACrD,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAE3C,8OAAC;4BAAE,WAAU;sCAA6D;;;;;;;;;;;;8BAM5E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,8OAAC,oIAAA,CAAA,kBAAe;0CACb,cAAc,IAAI,CAAC,aAAa,sBAC/B,8OAAC,oIAAA,CAAA,eAAY;wCAAsB,WAAU;kDAC3C,cAAA,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;wDAAI,WAAU;kEACZ;+DAAI,MAAM,YAAY,MAAM;yDAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtC,8OAAC,kMAAA,CAAA,OAAI;gEAAS,WAAU;+DAAb;;;;;;;;;;kEAIf,8OAAC;wDAAW,WAAU;;4DAAoC;4DACtD,YAAY,OAAO;4DAAC;;;;;;;kEAGxB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EACb,YAAY,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;0EAG7B,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;kFAAiB,YAAY,IAAI;;;;;;kFAChD,8OAAC;wEAAI,WAAU;kFAAiC,YAAY,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCArBvD,YAAY,EAAE;;;;;;;;;;0CA6BrC,8OAAC,oIAAA,CAAA,mBAAgB;;;;;0CACjB,8OAAC,oIAAA,CAAA,eAAY;;;;;;;;;;;;;;;;8BAIjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAIlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAA8G;;;;;;kDAGhI,8OAAC;wCAAO,WAAU;kDAAyI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzK", "debugId": null}}, {"offset": {"line": 1930, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/dance/dance-studio/src/components/ui/accordion.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\nimport { ChevronDownIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Accordion({\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Root>) {\n  return <AccordionPrimitive.Root data-slot=\"accordion\" {...props} />\n}\n\nfunction AccordionItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Item>) {\n  return (\n    <AccordionPrimitive.Item\n      data-slot=\"accordion-item\"\n      className={cn(\"border-b last:border-b-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AccordionTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Trigger>) {\n  return (\n    <AccordionPrimitive.Header className=\"flex\">\n      <AccordionPrimitive.Trigger\n        data-slot=\"accordion-trigger\"\n        className={cn(\n          \"focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <ChevronDownIcon className=\"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200\" />\n      </AccordionPrimitive.Trigger>\n    </AccordionPrimitive.Header>\n  )\n}\n\nfunction AccordionContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Content>) {\n  return (\n    <AccordionPrimitive.Content\n      data-slot=\"accordion-content\"\n      className=\"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm\"\n      {...props}\n    >\n      <div className={cn(\"pt-0 pb-4\", className)}>{children}</div>\n    </AccordionPrimitive.Content>\n  )\n}\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,UAAU,EACjB,GAAG,OACkD;IACrD,qBAAO,8OAAC,qKAAA,CAAA,OAAuB;QAAC,aAAU;QAAa,GAAG,KAAK;;;;;;AACjE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,8OAAC,qKAAA,CAAA,SAAyB;QAAC,WAAU;kBACnC,cAAA,8OAAC,qKAAA,CAAA,UAA0B;YACzB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8SACA;YAED,GAAG,KAAK;;gBAER;8BACD,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,8OAAC,qKAAA,CAAA,UAA0B;QACzB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBAAa;;;;;;;;;;;AAGnD", "debugId": null}}, {"offset": {"line": 2018, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/dance/dance-studio/src/components/faq-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from \"@/components/ui/accordion\";\n\nconst faqs = [\n  {\n    question: \"What age groups do you cater to?\",\n    answer: \"We welcome dancers of all ages! Our classes range from kids (5-12 years) to adults (18+). We have specialized programs for different age groups to ensure age-appropriate instruction and a comfortable learning environment.\"\n  },\n  {\n    question: \"Do I need any prior dance experience?\",\n    answer: \"Not at all! We have beginner-friendly classes for all dance styles. Our instructors are experienced in teaching complete beginners and will guide you step by step. Everyone starts somewhere, and we're here to support your journey.\"\n  },\n  {\n    question: \"What should I wear to dance classes?\",\n    answer: \"Wear comfortable, stretchy clothing that allows free movement. For most classes, athletic wear or leggings with a fitted top work well. Bring a water bottle and a small towel. Specific footwear requirements vary by dance style - we'll let you know what's needed for your chosen class.\"\n  },\n  {\n    question: \"Can I try a class before committing to a package?\",\n    answer: \"Absolutely! We offer a free trial class for all new students. This gives you a chance to experience our teaching style, meet the instructors, and see if the class is right for you before making any commitment.\"\n  },\n  {\n    question: \"How often should I attend classes?\",\n    answer: \"For best results, we recommend attending classes 2-3 times per week. However, even once a week can be beneficial. Our flexible packages allow you to choose what works best for your schedule and goals.\"\n  },\n  {\n    question: \"Do you offer private lessons?\",\n    answer: \"Yes! We offer private and semi-private lessons for students who want personalized attention, are preparing for competitions, or prefer one-on-one instruction. Contact us to discuss scheduling and pricing.\"\n  },\n  {\n    question: \"Are there performance opportunities?\",\n    answer: \"Definitely! We organize regular showcases, participate in local festivals, and compete in regional competitions. Participation is always voluntary, but it's a great way to build confidence and showcase your progress.\"\n  },\n  {\n    question: \"What safety measures do you have in place?\",\n    answer: \"Your safety is our priority. Our studio has proper flooring, mirrors, and ventilation. We maintain clean facilities, have first aid available, and our instructors are trained in injury prevention. We also follow all local health guidelines.\"\n  }\n];\n\nexport function FAQSection() {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  return (\n    <section className=\"section-padding bg-muted/50\" ref={ref}>\n      <div className=\"max-w-4xl mx-auto\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            Frequently Asked <span className=\"gradient-text\">Questions</span>\n          </h2>\n          <p className=\"text-lg sm:text-xl text-muted-foreground\">\n            Got questions? We've got answers! Here are some common questions \n            from our dance community.\n          </p>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.2 }}\n        >\n          <Accordion type=\"single\" collapsible className=\"space-y-4\">\n            {faqs.map((faq, index) => (\n              <AccordionItem \n                key={index} \n                value={`item-${index}`}\n                className=\"bg-background border border-border rounded-lg px-6\"\n              >\n                <AccordionTrigger className=\"text-left hover:no-underline py-6\">\n                  <span className=\"font-semibold\">{faq.question}</span>\n                </AccordionTrigger>\n                <AccordionContent className=\"pb-6 text-muted-foreground leading-relaxed\">\n                  {faq.answer}\n                </AccordionContent>\n              </AccordionItem>\n            ))}\n          </Accordion>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          className=\"mt-16 text-center\"\n        >\n          <div className=\"bg-background border border-border rounded-lg p-8\">\n            <h3 className=\"text-xl font-bold mb-4\">Still Have Questions?</h3>\n            <p className=\"text-muted-foreground mb-6\">\n              Can't find what you're looking for? Our friendly team is here to help \n              with any questions about classes, schedules, or getting started.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"bg-primary hover:bg-primary/90 text-primary-foreground px-6 py-3 rounded-lg font-semibold transition-colors\">\n                Contact Us\n              </button>\n              <button className=\"border border-primary text-primary hover:bg-primary hover:text-primary-foreground px-6 py-3 rounded-lg font-semibold transition-colors\">\n                Book Free Trial\n              </button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,OAAO;IACX;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;CACD;AAEM,SAAS;IACd,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,qBACE,8OAAC;QAAQ,WAAU;QAA8B,KAAK;kBACpD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;;gCAAkD;8CAC7C,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAEnD,8OAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;;8BAM1D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAExC,cAAA,8OAAC,qIAAA,CAAA,YAAS;wBAAC,MAAK;wBAAS,WAAW;wBAAC,WAAU;kCAC5C,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,8OAAC,qIAAA,CAAA,gBAAa;gCAEZ,OAAO,CAAC,KAAK,EAAE,OAAO;gCACtB,WAAU;;kDAEV,8OAAC,qIAAA,CAAA,mBAAgB;wCAAC,WAAU;kDAC1B,cAAA,8OAAC;4CAAK,WAAU;sDAAiB,IAAI,QAAQ;;;;;;;;;;;kDAE/C,8OAAC,qIAAA,CAAA,mBAAgB;wCAAC,WAAU;kDACzB,IAAI,MAAM;;;;;;;+BARR;;;;;;;;;;;;;;;8BAeb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyB;;;;;;0CACvC,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAI1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAA8G;;;;;;kDAGhI,8OAAC;wCAAO,WAAU;kDAAyI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzK", "debugId": null}}]}