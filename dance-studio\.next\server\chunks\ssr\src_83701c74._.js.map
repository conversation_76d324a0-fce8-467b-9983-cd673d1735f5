{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/dance/dance-studio/src/components/packages-hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\n\nexport function PackagesHero() {\n  return (\n    <section className=\"relative min-h-[60vh] flex items-center justify-center overflow-hidden bg-gradient-to-br from-primary/10 to-purple-500/10\">\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n        >\n          <h1 className=\"text-4xl sm:text-5xl lg:text-6xl font-bold mb-6\">\n            Our <span className=\"gradient-text\">Packages</span>\n          </h1>\n          <p className=\"text-xl sm:text-2xl text-muted-foreground max-w-3xl mx-auto\">\n            Flexible pricing options designed to fit your lifestyle and dance goals. \n            Start your journey with us today!\n          </p>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;;kCAE5B,8OAAC;wBAAG,WAAU;;4BAAkD;0CAC1D,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;;kCAEtC,8OAAC;wBAAE,WAAU;kCAA8D;;;;;;;;;;;;;;;;;;;;;;AAQrF", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/dance/dance-studio/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/dance/dance-studio/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/dance/dance-studio/src/lib/data.ts"], "sourcesContent": ["// Mock data for the dance studio\n\nexport interface DanceClass {\n  id: string;\n  name: string;\n  description: string;\n  category: string;\n  ageGroup: string;\n  duration: string;\n  level: string;\n  image: string;\n  schedule: {\n    day: string;\n    time: string;\n  }[];\n}\n\nexport interface Instructor {\n  id: string;\n  name: string;\n  bio: string;\n  specialties: string[];\n  experience: string;\n  image: string;\n}\n\nexport interface Testimonial {\n  id: string;\n  name: string;\n  role: string;\n  content: string;\n  rating: number;\n  image: string;\n}\n\nexport interface Package {\n  id: string;\n  name: string;\n  description: string;\n  price: number;\n  duration: string;\n  features: string[];\n  popular: boolean;\n}\n\nexport const danceClasses: DanceClass[] = [\n  {\n    id: \"1\",\n    name: \"Hip-Hop Fundamentals\",\n    description: \"Learn the basics of hip-hop dance with energetic moves and urban style.\",\n    category: \"Hip-Hop\",\n    ageGroup: \"13+\",\n    duration: \"60 minutes\",\n    level: \"Beginner\",\n    image: \"/images/hip-hop.jpg\",\n    schedule: [\n      { day: \"Monday\", time: \"6:00 PM - 7:00 PM\" },\n      { day: \"Wednesday\", time: \"6:00 PM - 7:00 PM\" },\n      { day: \"Friday\", time: \"6:00 PM - 7:00 PM\" }\n    ]\n  },\n  {\n    id: \"2\",\n    name: \"Contemporary Flow\",\n    description: \"Express yourself through fluid movements and emotional storytelling.\",\n    category: \"Contemporary\",\n    ageGroup: \"16+\",\n    duration: \"75 minutes\",\n    level: \"Intermediate\",\n    image: \"/images/contemporary.jpg\",\n    schedule: [\n      { day: \"Tuesday\", time: \"7:00 PM - 8:15 PM\" },\n      { day: \"Thursday\", time: \"7:00 PM - 8:15 PM\" }\n    ]\n  },\n  {\n    id: \"3\",\n    name: \"Classical Dance\",\n    description: \"Traditional Nepali and Indian classical dance forms with grace and precision.\",\n    category: \"Classical\",\n    ageGroup: \"8+\",\n    duration: \"90 minutes\",\n    level: \"All Levels\",\n    image: \"/images/classical.jpg\",\n    schedule: [\n      { day: \"Saturday\", time: \"10:00 AM - 11:30 AM\" },\n      { day: \"Sunday\", time: \"10:00 AM - 11:30 AM\" }\n    ]\n  },\n  {\n    id: \"4\",\n    name: \"Zumba Fitness\",\n    description: \"High-energy dance fitness combining Latin rhythms with easy-to-follow moves.\",\n    category: \"Zumba\",\n    ageGroup: \"18+\",\n    duration: \"45 minutes\",\n    level: \"All Levels\",\n    image: \"/images/zumba.jpg\",\n    schedule: [\n      { day: \"Monday\", time: \"7:00 AM - 7:45 AM\" },\n      { day: \"Wednesday\", time: \"7:00 AM - 7:45 AM\" },\n      { day: \"Friday\", time: \"7:00 AM - 7:45 AM\" }\n    ]\n  },\n  {\n    id: \"5\",\n    name: \"Kids Dance Party\",\n    description: \"Fun and creative dance classes designed specifically for children.\",\n    category: \"Kids\",\n    ageGroup: \"5-12\",\n    duration: \"45 minutes\",\n    level: \"Beginner\",\n    image: \"/images/kids-dance.jpg\",\n    schedule: [\n      { day: \"Saturday\", time: \"2:00 PM - 2:45 PM\" },\n      { day: \"Sunday\", time: \"2:00 PM - 2:45 PM\" }\n    ]\n  }\n];\n\nexport const instructors: Instructor[] = [\n  {\n    id: \"1\",\n    name: \"Priya Sharma\",\n    bio: \"Lead instructor with over 8 years of experience in contemporary and classical dance.\",\n    specialties: [\"Contemporary\", \"Classical\", \"Choreography\"],\n    experience: \"8+ years\",\n    image: \"/images/instructor-1.jpg\"\n  },\n  {\n    id: \"2\",\n    name: \"Raj Thapa\",\n    bio: \"Hip-hop specialist who brings street dance culture to Biratnagar with authentic moves.\",\n    specialties: [\"Hip-Hop\", \"Breaking\", \"Urban Dance\"],\n    experience: \"6+ years\",\n    image: \"/images/instructor-2.jpg\"\n  },\n  {\n    id: \"3\",\n    name: \"Maya Gurung\",\n    bio: \"Certified Zumba instructor passionate about fitness and dance fusion.\",\n    specialties: [\"Zumba\", \"Dance Fitness\", \"Latin Dance\"],\n    experience: \"5+ years\",\n    image: \"/images/instructor-3.jpg\"\n  },\n  {\n    id: \"4\",\n    name: \"Arjun Rai\",\n    bio: \"Kids dance specialist who makes learning fun and engaging for young dancers.\",\n    specialties: [\"Kids Dance\", \"Creative Movement\", \"Musical Theatre\"],\n    experience: \"4+ years\",\n    image: \"/images/instructor-4.jpg\"\n  }\n];\n\nexport const testimonials: Testimonial[] = [\n  {\n    id: \"1\",\n    name: \"Sita Poudel\",\n    role: \"Student\",\n    content: \"DanceStudio has transformed my confidence and fitness. The instructors are amazing and the community is so welcoming!\",\n    rating: 5,\n    image: \"/images/testimonial-1.jpg\"\n  },\n  {\n    id: \"2\",\n    name: \"Ramesh Shrestha\",\n    role: \"Parent\",\n    content: \"My daughter loves her kids dance classes here. She's learned so much and made great friends. Highly recommend!\",\n    rating: 5,\n    image: \"/images/testimonial-2.jpg\"\n  },\n  {\n    id: \"3\",\n    name: \"Anita Karki\",\n    role: \"Student\",\n    content: \"The contemporary classes are incredible. I've grown so much as a dancer and performer. Thank you DanceStudio!\",\n    rating: 5,\n    image: \"/images/testimonial-3.jpg\"\n  }\n];\n\nexport const packages: Package[] = [\n  {\n    id: \"1\",\n    name: \"Drop-In Class\",\n    description: \"Perfect for trying out classes or occasional visits\",\n    price: 500,\n    duration: \"Single Class\",\n    features: [\n      \"Access to any single class\",\n      \"No commitment required\",\n      \"Great for beginners\"\n    ],\n    popular: false\n  },\n  {\n    id: \"2\",\n    name: \"Weekly Package\",\n    description: \"Ideal for regular practice and skill development\",\n    price: 1800,\n    duration: \"1 Week\",\n    features: [\n      \"Unlimited classes for 1 week\",\n      \"Access to all dance styles\",\n      \"Priority booking\",\n      \"Free water bottle\"\n    ],\n    popular: true\n  },\n  {\n    id: \"3\",\n    name: \"Monthly Unlimited\",\n    description: \"Best value for dedicated dancers\",\n    price: 6000,\n    duration: \"1 Month\",\n    features: [\n      \"Unlimited classes for 1 month\",\n      \"Access to all dance styles\",\n      \"Priority booking\",\n      \"Free merchandise\",\n      \"Guest pass (1 per month)\"\n    ],\n    popular: false\n  }\n];\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;;AA6C1B,MAAM,eAA6B;IACxC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,OAAO;QACP,OAAO;QACP,UAAU;YACR;gBAAE,KAAK;gBAAU,MAAM;YAAoB;YAC3C;gBAAE,KAAK;gBAAa,MAAM;YAAoB;YAC9C;gBAAE,KAAK;gBAAU,MAAM;YAAoB;SAC5C;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,OAAO;QACP,OAAO;QACP,UAAU;YACR;gBAAE,KAAK;gBAAW,MAAM;YAAoB;YAC5C;gBAAE,KAAK;gBAAY,MAAM;YAAoB;SAC9C;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,OAAO;QACP,OAAO;QACP,UAAU;YACR;gBAAE,KAAK;gBAAY,MAAM;YAAsB;YAC/C;gBAAE,KAAK;gBAAU,MAAM;YAAsB;SAC9C;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,OAAO;QACP,OAAO;QACP,UAAU;YACR;gBAAE,KAAK;gBAAU,MAAM;YAAoB;YAC3C;gBAAE,KAAK;gBAAa,MAAM;YAAoB;YAC9C;gBAAE,KAAK;gBAAU,MAAM;YAAoB;SAC5C;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,OAAO;QACP,OAAO;QACP,UAAU;YACR;gBAAE,KAAK;gBAAY,MAAM;YAAoB;YAC7C;gBAAE,KAAK;gBAAU,MAAM;YAAoB;SAC5C;IACH;CACD;AAEM,MAAM,cAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,aAAa;YAAC;YAAgB;YAAa;SAAe;QAC1D,YAAY;QACZ,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,aAAa;YAAC;YAAW;YAAY;SAAc;QACnD,YAAY;QACZ,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,aAAa;YAAC;YAAS;YAAiB;SAAc;QACtD,YAAY;QACZ,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,aAAa;YAAC;YAAc;YAAqB;SAAkB;QACnE,YAAY;QACZ,OAAO;IACT;CACD;AAEM,MAAM,eAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,OAAO;IACT;CACD;AAEM,MAAM,WAAsB;IACjC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU;QACV,UAAU;YACR;YACA;YACA;SACD;QACD,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU;QACV,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU;QACV,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;IACX;CACD", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/dance/dance-studio/src/lib/queries.ts"], "sourcesContent": ["import { useQuery } from '@tanstack/react-query';\nimport { danceClasses, instructors, testimonials, packages } from './data';\n\n// Simulate API delay\nconst delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));\n\n// Mock API functions\nexport const fetchDanceClasses = async () => {\n  await delay(500);\n  return danceClasses;\n};\n\nexport const fetchInstructors = async () => {\n  await delay(500);\n  return instructors;\n};\n\nexport const fetchTestimonials = async () => {\n  await delay(500);\n  return testimonials;\n};\n\nexport const fetchPackages = async () => {\n  await delay(500);\n  return packages;\n};\n\n// React Query hooks\nexport const useDanceClasses = () => {\n  return useQuery({\n    queryKey: ['danceClasses'],\n    queryFn: fetchDanceClasses,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n};\n\nexport const useInstructors = () => {\n  return useQuery({\n    queryKey: ['instructors'],\n    queryFn: fetchInstructors,\n    staleTime: 5 * 60 * 1000,\n  });\n};\n\nexport const useTestimonials = () => {\n  return useQuery({\n    queryKey: ['testimonials'],\n    queryFn: fetchTestimonials,\n    staleTime: 5 * 60 * 1000,\n  });\n};\n\nexport const usePackages = () => {\n  return useQuery({\n    queryKey: ['packages'],\n    queryFn: fetchPackages,\n    staleTime: 5 * 60 * 1000,\n  });\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEA,qBAAqB;AACrB,MAAM,QAAQ,CAAC,KAAe,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AAGlE,MAAM,oBAAoB;IAC/B,MAAM,MAAM;IACZ,OAAO,kHAAA,CAAA,eAAY;AACrB;AAEO,MAAM,mBAAmB;IAC9B,MAAM,MAAM;IACZ,OAAO,kHAAA,CAAA,cAAW;AACpB;AAEO,MAAM,oBAAoB;IAC/B,MAAM,MAAM;IACZ,OAAO,kHAAA,CAAA,eAAY;AACrB;AAEO,MAAM,gBAAgB;IAC3B,MAAM,MAAM;IACZ,OAAO,kHAAA,CAAA,WAAQ;AACjB;AAGO,MAAM,kBAAkB;IAC7B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAe;QAC1B,SAAS;QACT,WAAW,IAAI,KAAK;IACtB;AACF;AAEO,MAAM,iBAAiB;IAC5B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAc;QACzB,SAAS;QACT,WAAW,IAAI,KAAK;IACtB;AACF;AAEO,MAAM,kBAAkB;IAC7B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAe;QAC1B,SAAS;QACT,WAAW,IAAI,KAAK;IACtB;AACF;AAEO,MAAM,cAAc;IACzB,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAW;QACtB,SAAS;QACT,WAAW,IAAI,KAAK;IACtB;AACF", "debugId": null}}, {"offset": {"line": 530, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/dance/dance-studio/src/components/packages-grid.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Check, Star, CreditCard, RefreshCw, Gift } from \"lucide-react\";\nimport { usePackages } from \"@/lib/queries\";\n\nexport function PackagesGrid() {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const { data: packages, isLoading } = usePackages();\n\n  if (isLoading) {\n    return (\n      <section className=\"section-padding bg-background\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n              Choose Your <span className=\"gradient-text\">Plan</span>\n            </h2>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {[1, 2, 3].map((i) => (\n              <div key={i} className=\"dance-card animate-pulse\">\n                <div className=\"h-48 bg-muted rounded-lg mb-4\" />\n                <div className=\"h-4 bg-muted rounded mb-2\" />\n                <div className=\"h-3 bg-muted rounded mb-4\" />\n                <div className=\"h-8 bg-muted rounded\" />\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section className=\"section-padding bg-background\" ref={ref}>\n      <div className=\"max-w-7xl mx-auto\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            Choose Your <span className=\"gradient-text\">Plan</span>\n          </h2>\n          <p className=\"text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto\">\n            Whether you're just starting out or ready to dive deep into dance, \n            we have the perfect package for your journey.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {packages?.map((pkg, index) => (\n            <motion.div\n              key={pkg.id}\n              initial={{ opacity: 0, y: 50 }}\n              animate={inView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.8, delay: index * 0.2 }}\n              className=\"relative\"\n            >\n              {pkg.popular && (\n                <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 z-10\">\n                  <Badge className=\"bg-primary text-primary-foreground px-4 py-1 flex items-center space-x-1\">\n                    <Star className=\"w-4 h-4\" />\n                    <span>Most Popular</span>\n                  </Badge>\n                </div>\n              )}\n              \n              <Card className={`dance-card h-full ${pkg.popular ? 'ring-2 ring-primary' : ''}`}>\n                <CardHeader className=\"text-center pb-8\">\n                  <CardTitle className=\"text-2xl mb-2\">{pkg.name}</CardTitle>\n                  <CardDescription className=\"text-base mb-6\">\n                    {pkg.description}\n                  </CardDescription>\n                  <div className=\"space-y-2\">\n                    <div className=\"text-4xl font-bold text-primary\">\n                      NPR {pkg.price.toLocaleString()}\n                    </div>\n                    <div className=\"text-sm text-muted-foreground\">\n                      {pkg.duration}\n                    </div>\n                  </div>\n                </CardHeader>\n                \n                <CardContent className=\"space-y-6\">\n                  <div className=\"space-y-3\">\n                    {pkg.features.map((feature, idx) => (\n                      <div key={idx} className=\"flex items-center space-x-3\">\n                        <div className=\"w-5 h-5 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0\">\n                          <Check className=\"w-3 h-3 text-primary\" />\n                        </div>\n                        <span className=\"text-sm\">{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n                  \n                  <Button \n                    className={`w-full ${pkg.popular \n                      ? 'bg-primary hover:bg-primary/90' \n                      : 'bg-secondary hover:bg-secondary/90 text-secondary-foreground'\n                    }`}\n                    size=\"lg\"\n                  >\n                    {pkg.name === \"Drop-In Class\" ? \"Book Single Class\" : \"Choose Plan\"}\n                  </Button>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          className=\"mt-16\"\n        >\n          <Card className=\"bg-primary/5 border-primary/20\">\n            <CardContent className=\"p-8 text-center\">\n              <h3 className=\"text-2xl font-bold mb-4\">Not Sure Which Package to Choose?</h3>\n              <p className=\"text-lg text-muted-foreground mb-6 max-w-2xl mx-auto\">\n                Start with our free trial class to experience our teaching style and studio atmosphere. \n                Our team will help you find the perfect package based on your goals and schedule.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <Button size=\"lg\" className=\"bg-primary hover:bg-primary/90 px-8\">\n                  Book Free Trial\n                </Button>\n                <Button variant=\"outline\" size=\"lg\" className=\"px-8\">\n                  Speak with Advisor\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.8 }}\n          className=\"mt-12 grid grid-cols-1 md:grid-cols-3 gap-6\"\n        >\n          <div className=\"text-center p-6\">\n            <div className=\"w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <CreditCard className=\"w-6 h-6 text-primary\" />\n            </div>\n            <h4 className=\"font-semibold mb-2\">Flexible Payment</h4>\n            <p className=\"text-sm text-muted-foreground\">\n              Multiple payment options including cash, card, and mobile payments\n            </p>\n          </div>\n          <div className=\"text-center p-6\">\n            <div className=\"w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <RefreshCw className=\"w-6 h-6 text-primary\" />\n            </div>\n            <h4 className=\"font-semibold mb-2\">Easy Transfers</h4>\n            <p className=\"text-sm text-muted-foreground\">\n              Switch between packages or pause your membership when needed\n            </p>\n          </div>\n          <div className=\"text-center p-6\">\n            <div className=\"w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <Gift className=\"w-6 h-6 text-primary\" />\n            </div>\n            <h4 className=\"font-semibold mb-2\">Special Offers</h4>\n            <p className=\"text-sm text-muted-foreground\">\n              Regular discounts for students, families, and long-term commitments\n            </p>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AARA;;;;;;;;;AAUO,SAAS;IACd,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,EAAE,MAAM,QAAQ,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD;IAEhD,IAAI,WAAW;QACb,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;;gCAAkD;8CAClD,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAGhD,8OAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;gCAAY,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;+BAJP;;;;;;;;;;;;;;;;;;;;;IAWtB;IAEA,qBACE,8OAAC;QAAQ,WAAU;QAAgC,KAAK;kBACtD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;;gCAAkD;8CAClD,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAE9C,8OAAC;4BAAE,WAAU;sCAA6D;;;;;;;;;;;;8BAM5E,8OAAC;oBAAI,WAAU;8BACZ,UAAU,IAAI,CAAC,KAAK,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,WAAU;;gCAET,IAAI,OAAO,kBACV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;;0DACf,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;8CAKZ,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAW,CAAC,kBAAkB,EAAE,IAAI,OAAO,GAAG,wBAAwB,IAAI;;sDAC9E,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAiB,IAAI,IAAI;;;;;;8DAC9C,8OAAC,gIAAA,CAAA,kBAAe;oDAAC,WAAU;8DACxB,IAAI,WAAW;;;;;;8DAElB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;gEAAkC;gEAC1C,IAAI,KAAK,CAAC,cAAc;;;;;;;sEAE/B,8OAAC;4DAAI,WAAU;sEACZ,IAAI,QAAQ;;;;;;;;;;;;;;;;;;sDAKnB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;8DACZ,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC1B,8OAAC;4DAAc,WAAU;;8EACvB,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;8EAEnB,8OAAC;oEAAK,WAAU;8EAAW;;;;;;;2DAJnB;;;;;;;;;;8DASd,8OAAC,kIAAA,CAAA,SAAM;oDACL,WAAW,CAAC,OAAO,EAAE,IAAI,OAAO,GAC5B,mCACA,gEACF;oDACF,MAAK;8DAEJ,IAAI,IAAI,KAAK,kBAAkB,sBAAsB;;;;;;;;;;;;;;;;;;;2BAlDvD,IAAI,EAAE;;;;;;;;;;8BA0DjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAuD;;;;;;8CAIpE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAAsC;;;;;;sDAGlE,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ7D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAI/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAI/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzD", "debugId": null}}, {"offset": {"line": 1112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/dance/dance-studio/src/components/ui/accordion.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\nimport { ChevronDownIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Accordion({\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Root>) {\n  return <AccordionPrimitive.Root data-slot=\"accordion\" {...props} />\n}\n\nfunction AccordionItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Item>) {\n  return (\n    <AccordionPrimitive.Item\n      data-slot=\"accordion-item\"\n      className={cn(\"border-b last:border-b-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AccordionTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Trigger>) {\n  return (\n    <AccordionPrimitive.Header className=\"flex\">\n      <AccordionPrimitive.Trigger\n        data-slot=\"accordion-trigger\"\n        className={cn(\n          \"focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <ChevronDownIcon className=\"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200\" />\n      </AccordionPrimitive.Trigger>\n    </AccordionPrimitive.Header>\n  )\n}\n\nfunction AccordionContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Content>) {\n  return (\n    <AccordionPrimitive.Content\n      data-slot=\"accordion-content\"\n      className=\"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm\"\n      {...props}\n    >\n      <div className={cn(\"pt-0 pb-4\", className)}>{children}</div>\n    </AccordionPrimitive.Content>\n  )\n}\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,UAAU,EACjB,GAAG,OACkD;IACrD,qBAAO,8OAAC,qKAAA,CAAA,OAAuB;QAAC,aAAU;QAAa,GAAG,KAAK;;;;;;AACjE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,8OAAC,qKAAA,CAAA,SAAyB;QAAC,WAAU;kBACnC,cAAA,8OAAC,qKAAA,CAAA,UAA0B;YACzB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8SACA;YAED,GAAG,KAAK;;gBAER;8BACD,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,8OAAC,qKAAA,CAAA,UAA0B;QACzB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBAAa;;;;;;;;;;;AAGnD", "debugId": null}}, {"offset": {"line": 1200, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/dance/dance-studio/src/components/packages-faq.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from \"@/components/ui/accordion\";\n\nconst faqs = [\n  {\n    question: \"Can I switch between packages?\",\n    answer: \"Yes! You can upgrade or change your package at any time. If you're switching to a higher-tier package, you'll only pay the difference. Package changes take effect from your next billing cycle.\"\n  },\n  {\n    question: \"What happens if I miss classes?\",\n    answer: \"For weekly and monthly packages, missed classes don't expire immediately. You can make up missed classes within a reasonable timeframe. We also offer the option to pause your membership for up to 2 weeks per month if needed.\"\n  },\n  {\n    question: \"Are there any additional fees?\",\n    answer: \"No hidden fees! The prices shown include everything you need for classes. The only additional cost might be for special workshops or performance costumes, which are always optional.\"\n  },\n  {\n    question: \"Do you offer family discounts?\",\n    answer: \"Yes! We offer a 15% discount when 2 or more family members join together. We also have special rates for students under 18 and senior citizens above 60.\"\n  },\n  {\n    question: \"Can I freeze my membership?\",\n    answer: \"Absolutely! Life happens, and we understand. You can freeze your membership for up to 3 months per year for reasons like travel, illness, or other commitments. Just give us 48 hours notice.\"\n  },\n  {\n    question: \"What's included in the free trial?\",\n    answer: \"Your free trial includes a full-length class of your choice, use of our facilities, and a consultation with one of our instructors to help you choose the right program for your goals.\"\n  }\n];\n\nexport function PackagesFAQ() {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  return (\n    <section className=\"section-padding bg-muted/50\" ref={ref}>\n      <div className=\"max-w-4xl mx-auto\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            Frequently Asked <span className=\"gradient-text\">Questions</span>\n          </h2>\n          <p className=\"text-lg sm:text-xl text-muted-foreground\">\n            Got questions about our packages? We've got answers!\n          </p>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.2 }}\n        >\n          <Accordion type=\"single\" collapsible className=\"space-y-4\">\n            {faqs.map((faq, index) => (\n              <AccordionItem \n                key={index} \n                value={`item-${index}`}\n                className=\"bg-background border border-border rounded-lg px-6\"\n              >\n                <AccordionTrigger className=\"text-left hover:no-underline py-6\">\n                  <span className=\"font-semibold\">{faq.question}</span>\n                </AccordionTrigger>\n                <AccordionContent className=\"pb-6 text-muted-foreground leading-relaxed\">\n                  {faq.answer}\n                </AccordionContent>\n              </AccordionItem>\n            ))}\n          </Accordion>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          className=\"mt-16 text-center\"\n        >\n          <div className=\"bg-background border border-border rounded-lg p-8\">\n            <h3 className=\"text-xl font-bold mb-4\">Still Have Questions?</h3>\n            <p className=\"text-muted-foreground mb-6\">\n              Our friendly team is here to help you find the perfect dance package for your needs.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"bg-primary hover:bg-primary/90 text-primary-foreground px-6 py-3 rounded-lg font-semibold transition-colors\">\n                Contact Us\n              </button>\n              <button className=\"border border-primary text-primary hover:bg-primary hover:text-primary-foreground px-6 py-3 rounded-lg font-semibold transition-colors\">\n                Schedule Call\n              </button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,OAAO;IACX;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;CACD;AAEM,SAAS;IACd,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,qBACE,8OAAC;QAAQ,WAAU;QAA8B,KAAK;kBACpD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;;gCAAkD;8CAC7C,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAEnD,8OAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;;8BAK1D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAExC,cAAA,8OAAC,qIAAA,CAAA,YAAS;wBAAC,MAAK;wBAAS,WAAW;wBAAC,WAAU;kCAC5C,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,8OAAC,qIAAA,CAAA,gBAAa;gCAEZ,OAAO,CAAC,KAAK,EAAE,OAAO;gCACtB,WAAU;;kDAEV,8OAAC,qIAAA,CAAA,mBAAgB;wCAAC,WAAU;kDAC1B,cAAA,8OAAC;4CAAK,WAAU;sDAAiB,IAAI,QAAQ;;;;;;;;;;;kDAE/C,8OAAC,qIAAA,CAAA,mBAAgB;wCAAC,WAAU;kDACzB,IAAI,MAAM;;;;;;;+BARR;;;;;;;;;;;;;;;8BAeb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyB;;;;;;0CACvC,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAA8G;;;;;;kDAGhI,8OAAC;wCAAO,WAAU;kDAAyI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzK", "debugId": null}}]}